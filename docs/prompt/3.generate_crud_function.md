# CRUD代码生成指令

## 任务概述
依次读取docs/sql/init.sql中DDL建表信息，生成基于数据表定义的完整CRUD功能，包括后端Java代码、前端Vue界面和菜单SQL。

## 数据源文件
- DDL文件：docs/sql/init.sql - 用于生成CRUD功能
- 数据字典文件：docs/sql/dict.sql - 用于生成下拉选项和展示值

## 代码生成规范

### 1. 通用要求
- **必须实现的功能**：
  - 列表查询、分页、条件筛选
  - 新增记录、编辑记录、删除记录、批量删除
  - 数据导入、数据导出、模板下载（模板下载链接放在导入弹窗内）
  - Excel模版需要中文表头,字段必须是全部的字段，不要漏掉任务业务字段
- **接口规范**：
  - 前后端接口地址和数据格式必须完全一致
  - 导入和导出功能必须使用POST请求方式
- **数据校验**：
  - 必须根据SQL定义的字段类型/长度在DTO和Query参数中添加对应校验注解
  - 必须覆盖查询/新增/编辑所有场景的输入校验

### 2. 后端实现规范（app模块）
- **代码位置**：所有后端代码必须放在app模块下
- **代码结构**：必须生成以下Java类：
  - Controller：处理HTTP请求
  - DTO：数据传输对象
  - Entity：数据库实体
  - Mapper：数据库操作接口和XML
  - Query：查询参数对象
  - Service：业务逻辑接口
  - ServiceImpl：业务逻辑实现
- **代码参考**：必须参照项目中已有的DwsEastZbcpxxb类生成对应代码
- **权限控制**：
  - @PreAuthorize注解格式：`@ss.hasPermi('模块名:操作:子操作')`
  - 模块名必须与@RequestMapping路径首单词严格对应（全小写）
  - 示例：`@PreAuthorize("@ss.hasPermi('system:user:list')")`
- **数据库操作**：
  - Mapper.xml的insert语句不得包含create_time/update_time/is_del字段
- **数据对象转换**：
  - 查询场景：Controller和Service层只能接受Query对象
  - 非查询场景：Controller和Service层只能接受DTO对象
  - Service层与Mapper层之间必须进行数据对象转换：
    - 请求处理：Service层DTO对象必须转为Mapper层的Entity对象
    - 响应处理：Mapper层Entity必须转为Service层DTO对象
- **禁用项**：
  - 严禁使用com.jd.lightning.common.utils.poi.ExcelUtil
  - 严禁使用java.util.List.of()方法，必须替换为(new ArrayList()).add(Object)模式
  - Controller层禁用toResult()方法，必须使用toAjax()方法
    - 正确示例：`return toAjax(service.addUser(user));`
    - 错误示例：`return toResult(service.addUser(user));`
- **命名规范**：
  - 类名必须基于表名生成，示例：
    - 表名：t_dws_prp_product（prp为模块名）
    - 类名：DwsPrpProduct[Controller|DTO|Entity|Mapper|Query|Service|ServiceImpl]
  - 生成的DTO、Query、Entity属性名和类型必须严格对应SQL定义的字段名和类型
  - 不得添加SQL定义之外的额外属性
  - Controller返回类型必须使用com.jd.lightning.common.core.domain.Result
  - Controller的@RequestMapping路径使用`模块名+小写类名前缀`，多个单词间用斜杠(/)分隔
  - 示例：DwsPrpProductController的@RequestMapping路径为/prp/product，prp为模块名

### 3. 前端实现规范（web模块）
- **代码位置**：所有前端代码必须放在web工程下
- **文件结构**：
  - 必须按照`web/src/views/模块名/小写单词1/小写单词2/index.vue`格式创建目录和文件
  - 模块名必须从表名提取，如表名为t_dws_prp_product, dws_prp为模块名
  - 其他单词必须从Controller类名提取
  - 示例：DwsPrpProductController → `web/src/views/prp/product/index.vue`
- **代码参考**：
  - 必须参照web/src/**/*.vue和web/src/**/*.js文件
- **数据校验**：必须对所有输入项进行校验，包括：
  - 列表页查询条件
  - 新增、编辑页的所有输入项
  - 校验规则必须参考DDL语句中的数据类型、格式及长度
- **导出功能实现**：handleExport()函数必须按以下模式实现：
  ```javascript
  handleExport() {
    this.download(
      'system/user/export',  // API端点路径
      {
        ...this.queryParams,  // 查询参数对象
      },
      `user_${new Date().getTime()}.xlsx`  // 带时间戳的文件名
    );
  }
  ```
- **页面布局**：查询列表页面的查询条件组件（包括输入框、下拉框、日期等）一行最多展示3个，多出的换行展示
- **下拉框实现**：所有下拉框代码必须按以下格式生成
```vue
<el-form-item label="某个下拉框" prop="${参数名}">
  <el-select
    v-model="queryParams.${参数名}"
    placeholder="点位符"
    clearable
    style="width: ${根据字段实际长度设置宽度}px"
  >
    <el-option
      v-for="dict in dict.type.${字典数据中的dict_type}"
      :key="dict.value"
      :label="dict.label"
      :value="dict.value"
    />
  </el-select>
</el-form-item>
```

### 4. 菜单SQL生成规范
- **写入SQL文件**：生成的菜单SQL必须写入docs/sql/menu.sql这个文件，禁止写入其他文件
- **格式示例**：
  vue页面路径：web/src/views/dur/liability/cash/flow/index --> component: dur/liability/cash/flow/index，path: liabilityCashFlow
  ```sql
  INSERT INTO alm.sys_menu (menu_name,parent_id,order_num,`path`,component,query,route_name,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
  ('现金流管理',1,1,'liabilityCashFlow','dur/liability/cash/flow/index','','',1,0,'C','0','0','dur:liability:cash:flow:list','user','admin','2025-04-10 12:44:11','',NULL,'现金流管理菜单'),
  ('现金流查询',100,1,'','','','',1,0,'F','0','0','dur:liability:cash:flow:query','#','admin','2025-04-10 12:44:11','',NULL,''),
  ('现金流新增',100,2,'','','','',1,0,'F','0','0','dur:liability:cash:flow:add','#','admin','2025-04-10 12:44:11','',NULL,''),
  ('现金流修改',100,3,'','','','',1,0,'F','0','0','dur:liability:cash:flow:edit','#','admin','2025-04-10 12:44:11','',NULL,''),
  ('现金流删除',100,4,'','','','',1,0,'F','0','0','dur:liability:cash:flow:remove','#','admin','2025-04-10 12:44:11','',NULL,''),
  ('现金流导出',100,5,'','','','',1,0,'F','0','0','dur:liability:cash:flow:export','#','admin','2025-04-10 12:44:11','',NULL,''),
  ('现金流导入',100,6,'','','','',1,0,'F','0','0','dur:liability:cash:flow:import','#','admin','2025-04-10 12:44:11','',NULL,'');
  ```
- **SQL文件名命名规范**：
  - 字典数据文件名：docs/sql/dict.sql
  - 菜单数据文件名：docs/sql/menu.sql

## 执行要求
- **代码生成位置**：必须严格按照上述规范在指定模块目录下生成代码
- **执行顺序**：先生成后端代码，再生成前端代码，最后生成菜单SQL
- **任务拆分**：当表字段信息超过30个时，拆分为多个分片任务完成字段补全，保证生成全量字段信息
