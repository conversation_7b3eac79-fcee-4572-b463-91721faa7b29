package com.reinsurance.query;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jd.lightning.common.core.domain.BaseQuery;
import javax.validation.constraints.Size;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import java.util.Date;

/**
 * 保单登记再保产品信息查询对象
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class DwsPrpProductQuery extends BaseQuery {
    
    private static final long serialVersionUID = 1L;

    /** 交易编码 */
    @Size(max = 64, message = "交易编码长度不能超过64个字符")
    private String transactionNo;

    /** 保险机构代码,唯一固定值000166 */
    @Size(max = 64, message = "保险机构代码长度不能超过64个字符")
    private String companyCode;

    /** 再保险合同号码 */
    @Size(max = 64, message = "再保险合同号码长度不能超过64个字符")
    private String reInsuranceContNo;

    /** 再保险合同名称 */
    @Size(max = 256, message = "再保险合同名称长度不能超过256个字符")
    private String reInsuranceContName;

    /** 再保险合同简称 */
    @Size(max = 256, message = "再保险合同简称长度不能超过256个字符")
    private String reInsuranceContTitle;

    /** 再保险附约主合同号 */
    @Size(max = 64, message = "再保险附约主合同号长度不能超过64个字符")
    private String mainReInsuranceContNo;

    /** 合同附约类型,1:主合同,2:附约 */
    @Size(max = 4, message = "合同附约类型长度不能超过4个字符")
    private String contOrAmendmentType;

    /** 产品编码 */
    @Size(max = 64, message = "产品编码长度不能超过64个字符")
    private String productCode;

    /** 产品名称 */
    @Size(max = 128, message = "产品名称长度不能超过128个字符")
    private String productName;

    /** 团个性质,01:个险,02:团险,99:其他 */
    @Size(max = 4, message = "团个性质长度不能超过4个字符")
    private String gpFlag;

    /** 险类代码 */
    @Size(max = 64, message = "险类代码长度不能超过64个字符")
    private String productType;

    /** 责任代码 */
    @Size(max = 64, message = "责任代码长度不能超过64个字符")
    private String liabilityCode;

    /** 责任名称 */
    @Size(max = 128, message = "责任名称长度不能超过128个字符")
    private String liabilityName;

    /** 再保险公司代码 */
    @Size(max = 64, message = "再保险公司代码长度不能超过64个字符")
    private String reinsurerCode;

    /** 再保险公司名称 */
    @Size(max = 256, message = "再保险公司名称长度不能超过256个字符")
    private String reinsurerName;

    /** 再保人参与份额比例 */
    @Size(max = 32, message = "再保人参与份额比例长度不能超过32个字符")
    private String reinsuranceShare;

    /** 分保方式,1:溢额,2:成数,3:成数溢额混合,4:超赔 */
    @Size(max = 4, message = "分保方式长度不能超过4个字符")
    private String reinsurMode;

    /** 再保类型,01:事故超赔,02:修正共保方式,03:共保方式,04:风险保费方式,05:赔付率超赔,06:损失终止,07:险位超赔 */
    @Size(max = 4, message = "再保类型长度不能超过4个字符")
    private String reInsuranceType;

    /** 保险期限类型,10:长期险,11:定期(年),12:定期(岁),13:定期(两可),14:终身,20:短期险,21:短期,22:极短期,30:主险缴费期,90:未知 */
    @Size(max = 4, message = "保险期限类型长度不能超过4个字符")
    private String termType;

    /** 自留额 */
    @Size(max = 32, message = "自留额长度不能超过32个字符")
    private String retentionAmount;

    /** 自留比例 */
    @Size(max = 32, message = "自留比例长度不能超过32个字符")
    private String retentionPercentage;

    /** 分保比例 */
    @Size(max = 32, message = "分保比例长度不能超过32个字符")
    private String quotaSharePercentage;

    /** 所属年份 */
    private Integer reportYear;

    /** 所属月份 */
    @Min(value = 1, message = "所属月份最小值为1")
    @Max(value = 12, message = "所属月份最大值为12")
    private Integer reportMonth;

    /** 所属账期 */
    @Size(max = 64, message = "所属账期长度不能超过64个字符")
    private String accountPeriod;

    /** 数据来源,0:系统,1:人工 */
    private Integer dataSource;

    /** 推送状态,0:未推送,1:已推送 */
    private Integer pushStatus;

    /** 推送日期 */
    private Date pushDate;

    /** 推送人 */
    @Size(max = 64, message = "推送人长度不能超过64个字符")
    private String pushBy;
}
